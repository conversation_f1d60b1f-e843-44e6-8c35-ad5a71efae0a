-- Merging decision tree log ---
application
INJECTED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:2:5-33:19
INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml
MERGED from [:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-5.6.11\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-5.6.11\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_database] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_database-11.3.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_database] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_database-11.3.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_storage] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_storage-12.4.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_storage] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_storage-12.4.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-3.15.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-3.15.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79b3d919b76c49dd55792c935cbb2444\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79b3d919b76c49dd55792c935cbb2444\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da1a171ba1bed19c372775a69249e748\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da1a171ba1bed19c372775a69249e748\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a233fc03b1203b3c2954e84ca9dbd758\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a233fc03b1203b3c2954e84ca9dbd758\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a830c941d7b9deaf7bd1f8494e96b4ff\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a830c941d7b9deaf7bd1f8494e96b4ff\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3e9523f195a83a92c59092a64a4f48f\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3e9523f195a83a92c59092a64a4f48f\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d2ed2d712f5e817992f6fd253969006\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d2ed2d712f5e817992f6fd253969006\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-32:19
MERGED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488888695e2d7dd89ec8d365a99761fe\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488888695e2d7dd89ec8d365a99761fe\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce52788a58cccaeb8e26ccb3742a5810\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce52788a58cccaeb8e26ccb3742a5810\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f84ab3ff6a97e53fb3163820143e7e79\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f84ab3ff6a97e53fb3163820143e7e79\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9b4c5b4a3ce09d7eae518189502b57f\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9b4c5b4a3ce09d7eae518189502b57f\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50c656402c0e3652f4de646e0e64783d\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50c656402c0e3652f4de646e0e64783d\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf525084326100102c202edcfb7aabfd\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf525084326100102c202edcfb7aabfd\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80d381bf084c21e18706da6716588126\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80d381bf084c21e18706da6716588126\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13d0c2544ad01fcdb01c4e62d7089728\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13d0c2544ad01fcdb01c4e62d7089728\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b426ae45ba34699527b81c12c814bb78\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b426ae45ba34699527b81c12c814bb78\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e565694bd8e7fc6bedaaac67255baf8f\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e565694bd8e7fc6bedaaac67255baf8f\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2dbfa2b099ab663030b8159eebacd12\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2dbfa2b099ab663030b8159eebacd12\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5568a35fadd1ad6d1c703f16a16e5540\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5568a35fadd1ad6d1c703f16a16e5540\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0d9d3675465ff69d847e2f781f20c61\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0d9d3675465ff69d847e2f781f20c61\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92a2169b5b2674c8bbe10e1b1d80da0f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92a2169b5b2674c8bbe10e1b1d80da0f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f74e7aec8d7941409c0ca3891ecaa09e\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f74e7aec8d7941409c0ca3891ecaa09e\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f08bc2171c491c5055f20402eb4a550\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f08bc2171c491c5055f20402eb4a550\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5838d9648eb8f6a5577a2ee0f60df4e7\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5838d9648eb8f6a5577a2ee0f60df4e7\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c01e83e37f8dfc0f7167fc9be00eb84\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c01e83e37f8dfc0f7167fc9be00eb84\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml
manifest
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:1:1-45:12
MERGED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:1:1-45:12
INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:shared_preferences_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\shared_preferences_android-2.4.10\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-5.6.11\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_database] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_database-11.3.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-48:12
MERGED from [:firebase_storage] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_storage-12.4.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-3.15.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:17:1-75:12
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79b3d919b76c49dd55792c935cbb2444\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:15:1-38:12
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da1a171ba1bed19c372775a69249e748\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a233fc03b1203b3c2954e84ca9dbd758\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a830c941d7b9deaf7bd1f8494e96b4ff\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3e9523f195a83a92c59092a64a4f48f\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:15:1-34:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d2ed2d712f5e817992f6fd253969006\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [:syncfusion_flutter_pdfviewer] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\syncfusion_flutter_pdfviewer-25.2.7\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:device_info_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\device_info_plus-9.1.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:package_info_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\package_info_plus-8.3.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-8.3.7\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:flutter_local_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-17.2.4\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-34:12
MERGED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-14:12
MERGED from [:connectivity_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-6.1.4\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:flutter_pdfview] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_pdfview-1.4.1+1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_plugin_android_lifecycle-2.0.28\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:google_sign_in_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\google_sign_in_android-6.2.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:path_provider_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\path_provider_android-2.2.17\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd79e8926f3586b575e2cf621318b1c2\transformed\media-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.mhiew:android-pdf-viewer:3.2.0-beta.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d94c72f88a38f0154bb6439f27ec4f40\transformed\jetified-android-pdf-viewer-3.2.0-beta.3\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9da39af860cbc6bfc2bc363bc8cb6b5\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488888695e2d7dd89ec8d365a99761fe\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:17:1-40:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0098a6e93522fecc805d8900172003dc\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a4dcd3e8c92efa77cfa193b658940d9\transformed\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce52788a58cccaeb8e26ccb3742a5810\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f84ab3ff6a97e53fb3163820143e7e79\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acf3d415d3116611637584964b2f5f0a\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\51b235d850b37c8d41b8481383af3675\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7b6dbe1342a4c3c5d120bbaedea1098\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9b4c5b4a3ce09d7eae518189502b57f\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50c656402c0e3652f4de646e0e64783d\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4935f9b8e8c94a882c131ec73fed821\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf525084326100102c202edcfb7aabfd\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80d381bf084c21e18706da6716588126\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13d0c2544ad01fcdb01c4e62d7089728\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\711446779dba7db0b2feff6800102eff\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b426ae45ba34699527b81c12c814bb78\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75e1bcd7a8b61b1e132d50e7766bfd37\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb98276ae8bdd946a2259a69bef27bdf\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d6ee565ab495ccfff8140c2f79bcdec\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700e90dac6e79f902dcf01395b76e20e\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e4c2e4e231b6a1dd462fb9cdc4ff977\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5cf1d9716b9650d5a0a992711d623bf8\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebeb29d7113d60e59d5e0acf92f847f3\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1682fe25d19efcf6ed57ec9bfdc01f5\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1da3d52bf544e2149dd46ce2061a20cf\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2e21678784b3eef6b33f576b1691a56\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7156a54e8f0502a1f8f57797d8b3e6e\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b91be3af319ede480d7185430690ee1\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c092edbccc16347970ed4f22e8da111a\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebca0abd79b4e189ce4efe6f41f957a4\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d51a44ab6b56289d4858158a1ad6dd\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1f85211ae6978d0218d51595a7f7e62\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74ed8ceaa55f9ae57c7b49acd30346c2\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba8599b6014e514325b6a37b5077642\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\02cddce53ae6e54afa639a8fcdb8b322\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7cbd00988e28f46cac66dd959ae6532\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\277aa05e63c0582e9a6c0fb9811696d7\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e565694bd8e7fc6bedaaac67255baf8f\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2dbfa2b099ab663030b8159eebacd12\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5568a35fadd1ad6d1c703f16a16e5540\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55520e4df2220e27f13f0bbb7467d11a\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a70ddd560199940b45ffc1a1c4db7f79\transformed\jetified-activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\308a2e77faa557d0bd706972416bde6a\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ddd5e7b38041965968ff0c456e5ef322\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ec70d4eb0351866340efe37c2014fef\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30b0283ed69cc5a036226586199afb07\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd3810ce99ffe1cc26d54f0b91968d35\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58a0920e123e93dd6aa702d27ab7530e\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ba2c056d15ac2bb85e5cc14a5abc112\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\45af1ebc35cbf9d2d2886a132166b73a\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4220b5ee723fba7acc664a2b5c85228\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a955e1aaa27844724e860bd6269b5afd\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c5a81a775ceb5f896270c240e3627f0\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d3f16dd28e73e73ebdd49d75e0396da\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a0d5794843604364eca179d4eb7b8a1\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b98555939aa5eea06aa9fccf2cf9525\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0d9d3675465ff69d847e2f781f20c61\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\285b6d6abe2dd4ba9515886e610a7341\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ccaaf8ba0ff17d4bbcf5fc9064ae7a64\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92a2169b5b2674c8bbe10e1b1d80da0f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30d6dde8f6241b92bb3c0336da77a097\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f74e7aec8d7941409c0ca3891ecaa09e\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f08bc2171c491c5055f20402eb4a550\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d0d33ecaece8d17a3b79a6bcd1ce82f\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5838d9648eb8f6a5577a2ee0f60df4e7\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\881c4abfce54f2ad198c7660c5ae4450\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff849810dfe34a0ab790c402787c149e\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4e9d33e8be7718e1e9997a4b8a9b898\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47506b70b93177473c53f4d5d7e77c91\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76a0dd850d9f18b6d5dce2588e83d61b\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\768c044a1b4db4aa31548ac9b4db55c8\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f09a22cdd213028a38958888c7d16e20\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\906fdc9be005a1f5d5e4f237f53c1744\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4ff29299e4a8bcb9a53684e4a36dfa8\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e1412f82216e9a99f9f845f4567c1f2\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.mhiew:pdfium-android:1.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73421b5fbca2a7450c9564d9adcf75af\transformed\jetified-pdfium-android-1.9.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df7cb56956681d928ef21d1ab2db38ac\transformed\jetified-protolite-well-known-types-18.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c01e83e37f8dfc0f7167fc9be00eb84\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:2:1-21:12
	package
		INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:1:11-69
queries
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:39:5-44:15
MERGED from [:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-8.3.7\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:15
MERGED from [:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-8.3.7\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:40:9-43:18
action#android.intent.action.PROCESS_TEXT
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:41:13-72
	android:name
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:41:21-70
data
ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:42:13-50
	android:mimeType
		ADDED from D:\20223\2025\legl92025\android\app\src\main\AndroidManifest.xml:42:19-48
uses-permission#android.permission.INTERNET
ADDED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml:6:5-66
MERGED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79b3d919b76c49dd55792c935cbb2444\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79b3d919b76c49dd55792c935cbb2444\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da1a171ba1bed19c372775a69249e748\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da1a171ba1bed19c372775a69249e748\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a233fc03b1203b3c2954e84ca9dbd758\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a233fc03b1203b3c2954e84ca9dbd758\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a830c941d7b9deaf7bd1f8494e96b4ff\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a830c941d7b9deaf7bd1f8494e96b4ff\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [:google_sign_in_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\google_sign_in_android-6.2.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:google_sign_in_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\google_sign_in_android-6.2.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7b6dbe1342a4c3c5d120bbaedea1098\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7b6dbe1342a4c3c5d120bbaedea1098\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\711446779dba7db0b2feff6800102eff\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\711446779dba7db0b2feff6800102eff\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f08bc2171c491c5055f20402eb4a550\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f08bc2171c491c5055f20402eb4a550\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml:6:22-64
uses-sdk
INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml
INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml
MERGED from [:shared_preferences_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\shared_preferences_android-2.4.10\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\shared_preferences_android-2.4.10\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-5.6.11\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-5.6.11\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_database] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_database-11.3.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_database] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_database-11.3.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_storage] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_storage-12.4.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_storage] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_storage-12.4.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-3.15.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-3.15.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79b3d919b76c49dd55792c935cbb2444\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79b3d919b76c49dd55792c935cbb2444\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da1a171ba1bed19c372775a69249e748\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da1a171ba1bed19c372775a69249e748\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a233fc03b1203b3c2954e84ca9dbd758\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a233fc03b1203b3c2954e84ca9dbd758\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a830c941d7b9deaf7bd1f8494e96b4ff\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a830c941d7b9deaf7bd1f8494e96b4ff\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3e9523f195a83a92c59092a64a4f48f\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3e9523f195a83a92c59092a64a4f48f\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d2ed2d712f5e817992f6fd253969006\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d2ed2d712f5e817992f6fd253969006\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [:syncfusion_flutter_pdfviewer] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\syncfusion_flutter_pdfviewer-25.2.7\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:syncfusion_flutter_pdfviewer] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\syncfusion_flutter_pdfviewer-25.2.7\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:device_info_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\device_info_plus-9.1.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:device_info_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\device_info_plus-9.1.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\package_info_plus-8.3.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\package_info_plus-8.3.0\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-8.3.7\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-8.3.7\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-17.2.4\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-17.2.4\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-6.1.4\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-6.1.4\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_pdfview] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_pdfview-1.4.1+1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_pdfview] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_pdfview-1.4.1+1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_plugin_android_lifecycle-2.0.28\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_plugin_android_lifecycle-2.0.28\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\google_sign_in_android-6.2.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\google_sign_in_android-6.2.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\path_provider_android-2.2.17\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\path_provider_android-2.2.17\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd79e8926f3586b575e2cf621318b1c2\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd79e8926f3586b575e2cf621318b1c2\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.mhiew:android-pdf-viewer:3.2.0-beta.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d94c72f88a38f0154bb6439f27ec4f40\transformed\jetified-android-pdf-viewer-3.2.0-beta.3\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.mhiew:android-pdf-viewer:3.2.0-beta.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d94c72f88a38f0154bb6439f27ec4f40\transformed\jetified-android-pdf-viewer-3.2.0-beta.3\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9da39af860cbc6bfc2bc363bc8cb6b5\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9da39af860cbc6bfc2bc363bc8cb6b5\transformed\jetified-credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488888695e2d7dd89ec8d365a99761fe\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488888695e2d7dd89ec8d365a99761fe\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0098a6e93522fecc805d8900172003dc\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0098a6e93522fecc805d8900172003dc\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a4dcd3e8c92efa77cfa193b658940d9\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3a4dcd3e8c92efa77cfa193b658940d9\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce52788a58cccaeb8e26ccb3742a5810\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ce52788a58cccaeb8e26ccb3742a5810\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f84ab3ff6a97e53fb3163820143e7e79\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f84ab3ff6a97e53fb3163820143e7e79\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acf3d415d3116611637584964b2f5f0a\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\acf3d415d3116611637584964b2f5f0a\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\51b235d850b37c8d41b8481383af3675\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\51b235d850b37c8d41b8481383af3675\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7b6dbe1342a4c3c5d120bbaedea1098\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7b6dbe1342a4c3c5d120bbaedea1098\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9b4c5b4a3ce09d7eae518189502b57f\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9b4c5b4a3ce09d7eae518189502b57f\transformed\jetified-integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50c656402c0e3652f4de646e0e64783d\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50c656402c0e3652f4de646e0e64783d\transformed\jetified-firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4935f9b8e8c94a882c131ec73fed821\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4935f9b8e8c94a882c131ec73fed821\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf525084326100102c202edcfb7aabfd\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bf525084326100102c202edcfb7aabfd\transformed\jetified-play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80d381bf084c21e18706da6716588126\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80d381bf084c21e18706da6716588126\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13d0c2544ad01fcdb01c4e62d7089728\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\13d0c2544ad01fcdb01c4e62d7089728\transformed\jetified-firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\711446779dba7db0b2feff6800102eff\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\711446779dba7db0b2feff6800102eff\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b426ae45ba34699527b81c12c814bb78\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b426ae45ba34699527b81c12c814bb78\transformed\jetified-play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75e1bcd7a8b61b1e132d50e7766bfd37\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75e1bcd7a8b61b1e132d50e7766bfd37\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb98276ae8bdd946a2259a69bef27bdf\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cb98276ae8bdd946a2259a69bef27bdf\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d6ee565ab495ccfff8140c2f79bcdec\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d6ee565ab495ccfff8140c2f79bcdec\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700e90dac6e79f902dcf01395b76e20e\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\700e90dac6e79f902dcf01395b76e20e\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e4c2e4e231b6a1dd462fb9cdc4ff977\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2e4c2e4e231b6a1dd462fb9cdc4ff977\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5cf1d9716b9650d5a0a992711d623bf8\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5cf1d9716b9650d5a0a992711d623bf8\transformed\jetified-activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebeb29d7113d60e59d5e0acf92f847f3\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebeb29d7113d60e59d5e0acf92f847f3\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1682fe25d19efcf6ed57ec9bfdc01f5\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a1682fe25d19efcf6ed57ec9bfdc01f5\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1da3d52bf544e2149dd46ce2061a20cf\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1da3d52bf544e2149dd46ce2061a20cf\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2e21678784b3eef6b33f576b1691a56\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2e21678784b3eef6b33f576b1691a56\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7156a54e8f0502a1f8f57797d8b3e6e\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7156a54e8f0502a1f8f57797d8b3e6e\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b91be3af319ede480d7185430690ee1\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1b91be3af319ede480d7185430690ee1\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c092edbccc16347970ed4f22e8da111a\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c092edbccc16347970ed4f22e8da111a\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebca0abd79b4e189ce4efe6f41f957a4\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebca0abd79b4e189ce4efe6f41f957a4\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d51a44ab6b56289d4858158a1ad6dd\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3d51a44ab6b56289d4858158a1ad6dd\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1f85211ae6978d0218d51595a7f7e62\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1f85211ae6978d0218d51595a7f7e62\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74ed8ceaa55f9ae57c7b49acd30346c2\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\74ed8ceaa55f9ae57c7b49acd30346c2\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba8599b6014e514325b6a37b5077642\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1ba8599b6014e514325b6a37b5077642\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\02cddce53ae6e54afa639a8fcdb8b322\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\02cddce53ae6e54afa639a8fcdb8b322\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7cbd00988e28f46cac66dd959ae6532\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c7cbd00988e28f46cac66dd959ae6532\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\277aa05e63c0582e9a6c0fb9811696d7\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\277aa05e63c0582e9a6c0fb9811696d7\transformed\jetified-firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e565694bd8e7fc6bedaaac67255baf8f\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e565694bd8e7fc6bedaaac67255baf8f\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2dbfa2b099ab663030b8159eebacd12\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b2dbfa2b099ab663030b8159eebacd12\transformed\jetified-firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5568a35fadd1ad6d1c703f16a16e5540\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5568a35fadd1ad6d1c703f16a16e5540\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55520e4df2220e27f13f0bbb7467d11a\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\55520e4df2220e27f13f0bbb7467d11a\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a70ddd560199940b45ffc1a1c4db7f79\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a70ddd560199940b45ffc1a1c4db7f79\transformed\jetified-activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\308a2e77faa557d0bd706972416bde6a\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\308a2e77faa557d0bd706972416bde6a\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ddd5e7b38041965968ff0c456e5ef322\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ddd5e7b38041965968ff0c456e5ef322\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ec70d4eb0351866340efe37c2014fef\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ec70d4eb0351866340efe37c2014fef\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30b0283ed69cc5a036226586199afb07\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30b0283ed69cc5a036226586199afb07\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd3810ce99ffe1cc26d54f0b91968d35\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd3810ce99ffe1cc26d54f0b91968d35\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58a0920e123e93dd6aa702d27ab7530e\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\58a0920e123e93dd6aa702d27ab7530e\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ba2c056d15ac2bb85e5cc14a5abc112\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ba2c056d15ac2bb85e5cc14a5abc112\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\45af1ebc35cbf9d2d2886a132166b73a\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\45af1ebc35cbf9d2d2886a132166b73a\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4220b5ee723fba7acc664a2b5c85228\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4220b5ee723fba7acc664a2b5c85228\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a955e1aaa27844724e860bd6269b5afd\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a955e1aaa27844724e860bd6269b5afd\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c5a81a775ceb5f896270c240e3627f0\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c5a81a775ceb5f896270c240e3627f0\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d3f16dd28e73e73ebdd49d75e0396da\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3d3f16dd28e73e73ebdd49d75e0396da\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a0d5794843604364eca179d4eb7b8a1\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a0d5794843604364eca179d4eb7b8a1\transformed\localbroadcastmanager-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b98555939aa5eea06aa9fccf2cf9525\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3b98555939aa5eea06aa9fccf2cf9525\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0d9d3675465ff69d847e2f781f20c61\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0d9d3675465ff69d847e2f781f20c61\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\285b6d6abe2dd4ba9515886e610a7341\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\285b6d6abe2dd4ba9515886e610a7341\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ccaaf8ba0ff17d4bbcf5fc9064ae7a64\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ccaaf8ba0ff17d4bbcf5fc9064ae7a64\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92a2169b5b2674c8bbe10e1b1d80da0f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\92a2169b5b2674c8bbe10e1b1d80da0f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30d6dde8f6241b92bb3c0336da77a097\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30d6dde8f6241b92bb3c0336da77a097\transformed\jetified-firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f74e7aec8d7941409c0ca3891ecaa09e\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f74e7aec8d7941409c0ca3891ecaa09e\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f08bc2171c491c5055f20402eb4a550\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f08bc2171c491c5055f20402eb4a550\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d0d33ecaece8d17a3b79a6bcd1ce82f\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d0d33ecaece8d17a3b79a6bcd1ce82f\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5838d9648eb8f6a5577a2ee0f60df4e7\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5838d9648eb8f6a5577a2ee0f60df4e7\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\881c4abfce54f2ad198c7660c5ae4450\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\881c4abfce54f2ad198c7660c5ae4450\transformed\jetified-transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff849810dfe34a0ab790c402787c149e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ff849810dfe34a0ab790c402787c149e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4e9d33e8be7718e1e9997a4b8a9b898\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4e9d33e8be7718e1e9997a4b8a9b898\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47506b70b93177473c53f4d5d7e77c91\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47506b70b93177473c53f4d5d7e77c91\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76a0dd850d9f18b6d5dce2588e83d61b\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\76a0dd850d9f18b6d5dce2588e83d61b\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\768c044a1b4db4aa31548ac9b4db55c8\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\768c044a1b4db4aa31548ac9b4db55c8\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f09a22cdd213028a38958888c7d16e20\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f09a22cdd213028a38958888c7d16e20\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\906fdc9be005a1f5d5e4f237f53c1744\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\906fdc9be005a1f5d5e4f237f53c1744\transformed\jetified-googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4ff29299e4a8bcb9a53684e4a36dfa8\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4ff29299e4a8bcb9a53684e4a36dfa8\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e1412f82216e9a99f9f845f4567c1f2\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9e1412f82216e9a99f9f845f4567c1f2\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.github.mhiew:pdfium-android:1.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73421b5fbca2a7450c9564d9adcf75af\transformed\jetified-pdfium-android-1.9.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.mhiew:pdfium-android:1.9.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73421b5fbca2a7450c9564d9adcf75af\transformed\jetified-pdfium-android-1.9.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df7cb56956681d928ef21d1ab2db38ac\transformed\jetified-protolite-well-known-types-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\df7cb56956681d928ef21d1ab2db38ac\transformed\jetified-protolite-well-known-types-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c01e83e37f8dfc0f7167fc9be00eb84\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c01e83e37f8dfc0f7167fc9be00eb84\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:23:9-148
	android:targetSdkVersion
		INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\20223\2025\legl92025\android\app\src\debug\AndroidManifest.xml
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-5.6.11\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-5.6.11\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_database] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_database-11.3.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_database] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_database-11.3.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
MERGED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:9-39:19
MERGED from [:firebase_storage] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_storage-12.4.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_storage] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_storage-12.4.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-3.15.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-3.15.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79b3d919b76c49dd55792c935cbb2444\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79b3d919b76c49dd55792c935cbb2444\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da1a171ba1bed19c372775a69249e748\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da1a171ba1bed19c372775a69249e748\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a233fc03b1203b3c2954e84ca9dbd758\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a233fc03b1203b3c2954e84ca9dbd758\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a830c941d7b9deaf7bd1f8494e96b4ff\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a830c941d7b9deaf7bd1f8494e96b4ff\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3e9523f195a83a92c59092a64a4f48f\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3e9523f195a83a92c59092a64a4f48f\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d2ed2d712f5e817992f6fd253969006\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d2ed2d712f5e817992f6fd253969006\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f84ab3ff6a97e53fb3163820143e7e79\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f84ab3ff6a97e53fb3163820143e7e79\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f74e7aec8d7941409c0ca3891ecaa09e\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f74e7aec8d7941409c0ca3891ecaa09e\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f84ab3ff6a97e53fb3163820143e7e79\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f84ab3ff6a97e53fb3163820143e7e79\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar
ADDED from [:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_auth] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_auth-5.6.2\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar
ADDED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-5.6.11\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-5.6.11\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:cloud_firestore] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\cloud_firestore-5.6.11\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-134
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.database.FlutterFirebaseAppRegistrar
ADDED from [:firebase_database] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_database-11.3.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_database] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_database-11.3.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_database] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_database-11.3.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-127
uses-permission#android.permission.WAKE_LOCK
ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\711446779dba7db0b2feff6800102eff\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\711446779dba7db0b2feff6800102eff\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79b3d919b76c49dd55792c935cbb2444\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79b3d919b76c49dd55792c935cbb2444\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da1a171ba1bed19c372775a69249e748\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da1a171ba1bed19c372775a69249e748\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a233fc03b1203b3c2954e84ca9dbd758\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a233fc03b1203b3c2954e84ca9dbd758\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a830c941d7b9deaf7bd1f8494e96b4ff\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a830c941d7b9deaf7bd1f8494e96b4ff\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [:connectivity_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-6.1.4\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [:connectivity_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-6.1.4\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7b6dbe1342a4c3c5d120bbaedea1098\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7b6dbe1342a4c3c5d120bbaedea1098\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\711446779dba7db0b2feff6800102eff\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\711446779dba7db0b2feff6800102eff\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f08bc2171c491c5055f20402eb4a550\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f08bc2171c491c5055f20402eb4a550\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5838d9648eb8f6a5577a2ee0f60df4e7\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5838d9648eb8f6a5577a2ee0f60df4e7\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4ff29299e4a8bcb9a53684e4a36dfa8\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:7:5-79
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a4ff29299e4a8bcb9a53684e4a36dfa8\transformed\jetified-grpc-android-1.62.2\AndroidManifest.xml:7:5-79
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-76
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:23:5-77
MERGED from [:flutter_local_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-17.2.4\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
MERGED from [:flutter_local_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-17.2.4\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:22-74
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService
ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
	android:permission
		ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService
ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
receiver#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver
ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
	android:permission
		ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar
ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
	android:value
		ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
provider#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider
ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
	android:authorities
		ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
	android:exported
		ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
	android:initOrder
		ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
	android:name
		ADDED from [:firebase_messaging] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_messaging-15.2.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar
ADDED from [:firebase_storage] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_storage-12.4.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_storage] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_storage-12.4.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_storage] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_storage-12.4.9\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-126
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-3.15.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-3.15.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-3.15.1\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\897d516d875aa31779ac23575ea2d297\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
meta-data#com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar
ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79b3d919b76c49dd55792c935cbb2444\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79b3d919b76c49dd55792c935cbb2444\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79b3d919b76c49dd55792c935cbb2444\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:30:17-120
meta-data#com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar
ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79b3d919b76c49dd55792c935cbb2444\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79b3d919b76c49dd55792c935cbb2444\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79b3d919b76c49dd55792c935cbb2444\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:33:17-109
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da1a171ba1bed19c372775a69249e748\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da1a171ba1bed19c372775a69249e748\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da1a171ba1bed19c372775a69249e748\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da1a171ba1bed19c372775a69249e748\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da1a171ba1bed19c372775a69249e748\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da1a171ba1bed19c372775a69249e748\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\711446779dba7db0b2feff6800102eff\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\711446779dba7db0b2feff6800102eff\transformed\jetified-play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:30:13-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.1.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e35a474b8e17d1740e18628c5082a3a\transformed\jetified-firebase-messaging-24.1.2\AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar
ADDED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a233fc03b1203b3c2954e84ca9dbd758\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:30:13-32:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a233fc03b1203b3c2954e84ca9dbd758\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:32:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a233fc03b1203b3c2954e84ca9dbd758\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:31:17-118
meta-data#com.google.firebase.components:com.google.firebase.storage.StorageRegistrar
ADDED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a233fc03b1203b3c2954e84ca9dbd758\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a233fc03b1203b3c2954e84ca9dbd758\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a233fc03b1203b3c2954e84ca9dbd758\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:34:17-107
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a830c941d7b9deaf7bd1f8494e96b4ff\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a830c941d7b9deaf7bd1f8494e96b4ff\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a830c941d7b9deaf7bd1f8494e96b4ff\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a830c941d7b9deaf7bd1f8494e96b4ff\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a830c941d7b9deaf7bd1f8494e96b4ff\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a830c941d7b9deaf7bd1f8494e96b4ff\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar
ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3e9523f195a83a92c59092a64a4f48f\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3e9523f195a83a92c59092a64a4f48f\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3e9523f195a83a92c59092a64a4f48f\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar
ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3e9523f195a83a92c59092a64a4f48f\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3e9523f195a83a92c59092a64a4f48f\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b3e9523f195a83a92c59092a64a4f48f\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d2ed2d712f5e817992f6fd253969006\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d2ed2d712f5e817992f6fd253969006\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1d2ed2d712f5e817992f6fd253969006\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
intent#action:name:android.intent.action.GET_CONTENT+data:mimeType:*/*
ADDED from [:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-8.3.7\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
action#android.intent.action.GET_CONTENT
ADDED from [:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-8.3.7\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
	android:name
		ADDED from [:file_picker] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\file_picker-8.3.7\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
uses-permission#android.permission.VIBRATE
ADDED from [:flutter_local_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-17.2.4\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
	android:name
		ADDED from [:flutter_local_notifications] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\flutter_local_notifications-17.2.4\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
	android:resource
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488888695e2d7dd89ec8d365a99761fe\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488888695e2d7dd89ec8d365a99761fe\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488888695e2d7dd89ec8d365a99761fe\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488888695e2d7dd89ec8d365a99761fe\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488888695e2d7dd89ec8d365a99761fe\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488888695e2d7dd89ec8d365a99761fe\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488888695e2d7dd89ec8d365a99761fe\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488888695e2d7dd89ec8d365a99761fe\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488888695e2d7dd89ec8d365a99761fe\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488888695e2d7dd89ec8d365a99761fe\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488888695e2d7dd89ec8d365a99761fe\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488888695e2d7dd89ec8d365a99761fe\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488888695e2d7dd89ec8d365a99761fe\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488888695e2d7dd89ec8d365a99761fe\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\488888695e2d7dd89ec8d365a99761fe\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41fd65a6a815b5614b87b654f9cfd65e\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f84ab3ff6a97e53fb3163820143e7e79\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f84ab3ff6a97e53fb3163820143e7e79\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f84ab3ff6a97e53fb3163820143e7e79\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f84ab3ff6a97e53fb3163820143e7e79\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f84ab3ff6a97e53fb3163820143e7e79\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f84ab3ff6a97e53fb3163820143e7e79\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f84ab3ff6a97e53fb3163820143e7e79\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f84ab3ff6a97e53fb3163820143e7e79\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f84ab3ff6a97e53fb3163820143e7e79\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7b6dbe1342a4c3c5d120bbaedea1098\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7b6dbe1342a4c3c5d120bbaedea1098\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80d381bf084c21e18706da6716588126\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80d381bf084c21e18706da6716588126\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80d381bf084c21e18706da6716588126\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\80d381bf084c21e18706da6716588126\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0d9d3675465ff69d847e2f781f20c61\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0d9d3675465ff69d847e2f781f20c61\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5568a35fadd1ad6d1c703f16a16e5540\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5568a35fadd1ad6d1c703f16a16e5540\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5568a35fadd1ad6d1c703f16a16e5540\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.legal2025.yamy.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.legal2025.yamy.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f74e7aec8d7941409c0ca3891ecaa09e\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f74e7aec8d7941409c0ca3891ecaa09e\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f74e7aec8d7941409c0ca3891ecaa09e\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f08bc2171c491c5055f20402eb4a550\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5838d9648eb8f6a5577a2ee0f60df4e7\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5838d9648eb8f6a5577a2ee0f60df4e7\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f08bc2171c491c5055f20402eb4a550\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f08bc2171c491c5055f20402eb4a550\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f08bc2171c491c5055f20402eb4a550\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f08bc2171c491c5055f20402eb4a550\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f08bc2171c491c5055f20402eb4a550\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5838d9648eb8f6a5577a2ee0f60df4e7\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5838d9648eb8f6a5577a2ee0f60df4e7\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5838d9648eb8f6a5577a2ee0f60df4e7\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5838d9648eb8f6a5577a2ee0f60df4e7\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5838d9648eb8f6a5577a2ee0f60df4e7\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5838d9648eb8f6a5577a2ee0f60df4e7\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5838d9648eb8f6a5577a2ee0f60df4e7\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c01e83e37f8dfc0f7167fc9be00eb84\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c01e83e37f8dfc0f7167fc9be00eb84\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c01e83e37f8dfc0f7167fc9be00eb84\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c01e83e37f8dfc0f7167fc9be00eb84\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c01e83e37f8dfc0f7167fc9be00eb84\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
