{"logs": [{"outputFile": "com.legal2025.yamy.app-mergeDebugResources-67:/values-ko/values-ko.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\80d381bf084c21e18706da6716588126\\transformed\\jetified-play-services-base-18.1.0\\res\\values-ko\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,428,540,638,749,862,965,1056,1197,1296,1428,1542,1656,1771,1826,1880", "endColumns": "99,134,111,97,110,112,102,90,140,98,131,113,113,114,54,53,70", "endOffsets": "292,427,539,637,748,861,964,1055,1196,1295,1427,1541,1655,1770,1825,1879,1950"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3580,3684,3823,3939,4041,4156,4273,4380,4596,4741,4844,4980,5098,5216,5335,5394,5452", "endColumns": "103,138,115,101,114,116,106,94,144,102,135,117,117,118,58,57,74", "endOffsets": "3679,3818,3934,4036,4151,4268,4375,4470,4736,4839,4975,5093,5211,5330,5389,5447,5522"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cf478dec41eed746328fa8046755ba2\\transformed\\core-1.13.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2903,2995,3095,3189,3286,3382,3480,6334", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "2990,3090,3184,3281,3377,3475,3575,6430"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c9da39af860cbc6bfc2bc363bc8cb6b5\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,109", "endOffsets": "158,268"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2685,2793", "endColumns": "107,109", "endOffsets": "2788,2898"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\308a2e77faa557d0bd706972416bde6a\\transformed\\browser-1.8.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,141,234,337", "endColumns": "85,92,102,93", "endOffsets": "136,229,332,426"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "5593,5760,5853,5956", "endColumns": "85,92,102,93", "endOffsets": "5674,5848,5951,6045"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0098a6e93522fecc805d8900172003dc\\transformed\\preference-1.2.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,326,458,627,709", "endColumns": "65,80,73,131,168,81,75", "endOffsets": "166,247,321,453,622,704,780"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5527,5679,6050,6124,6435,6604,6686", "endColumns": "65,80,73,131,168,81,75", "endOffsets": "5588,5755,6119,6251,6599,6681,6757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3a4dcd3e8c92efa77cfa193b658940d9\\transformed\\appcompat-1.1.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,839,930,1022,1117,1211,1312,1405,1500,1594,1685,1776,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,75,90,91,94,93,100,92,94,93,90,90,78,97,93,94,99,96,99,151,93,77", "endOffsets": "197,291,392,474,572,678,758,834,925,1017,1112,1206,1307,1400,1495,1589,1680,1771,1850,1948,2042,2137,2237,2334,2434,2586,2680,2758"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,839,930,1022,1117,1211,1312,1405,1500,1594,1685,1776,1855,1953,2047,2142,2242,2339,2439,2591,6256", "endColumns": "96,93,100,81,97,105,79,75,90,91,94,93,100,92,94,93,90,90,78,97,93,94,99,96,99,151,93,77", "endOffsets": "197,291,392,474,572,678,758,834,925,1017,1112,1206,1307,1400,1495,1589,1680,1771,1850,1948,2042,2137,2237,2334,2434,2586,2680,6329"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5568a35fadd1ad6d1c703f16a16e5540\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ko\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "116", "endOffsets": "311"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4475", "endColumns": "120", "endOffsets": "4591"}}]}]}